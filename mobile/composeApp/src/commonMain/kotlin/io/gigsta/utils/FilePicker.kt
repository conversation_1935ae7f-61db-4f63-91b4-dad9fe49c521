package io.gigsta.utils

import androidx.compose.runtime.*
import io.github.vinceglb.filekit.compose.rememberFilePickerLauncher
import io.github.vinceglb.filekit.core.FileKit
import io.github.vinceglb.filekit.core.PickerMode
import io.github.vinceglb.filekit.core.PickerType

data class SelectedFile(
    val data: ByteArray,
    val name: String,
    val mimeType: String
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other == null || this::class != other::class) return false

        other as SelectedFile

        if (!data.contentEquals(other.data)) return false
        if (name != other.name) return false
        if (mimeType != other.mimeType) return false

        return true
    }

    override fun hashCode(): Int {
        var result = data.contentHashCode()
        result = 31 * result + name.hashCode()
        result = 31 * result + mimeType.hashCode()
        return result
    }
}

@Composable
fun rememberResumeFilePicker(
    onFileSelected: (SelectedFile) -> Unit,
    onError: (String) -> Unit = {}
): () -> Unit {
    val launcher = rememberFilePickerLauncher(
        type = PickerType.File(
            extensions = listOf("pdf", "doc", "docx")
        ),
        mode = PickerMode.Single
    ) { file ->
        if (file != null) {
            try {
                val fileData = file.readBytes()
                val fileName = file.name
                val mimeType = when (file.name.substringAfterLast('.').lowercase()) {
                    "pdf" -> "application/pdf"
                    "doc" -> "application/msword"
                    "docx" -> "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                    else -> "application/octet-stream"
                }
                
                // Check file size (5MB limit)
                if (fileData.size > 5 * 1024 * 1024) {
                    onError("File terlalu besar. Maksimal 5MB.")
                    return@rememberFilePickerLauncher
                }
                
                onFileSelected(SelectedFile(fileData, fileName, mimeType))
            } catch (e: Exception) {
                onError("Gagal membaca file: ${e.message}")
            }
        }
    }
    
    return { launcher.launch() }
}

@Composable
fun rememberImageFilePicker(
    onFileSelected: (SelectedFile) -> Unit,
    onError: (String) -> Unit = {}
): () -> Unit {
    val launcher = rememberFilePickerLauncher(
        type = PickerType.Image,
        mode = PickerMode.Single
    ) { file ->
        if (file != null) {
            try {
                val fileData = file.readBytes()
                val fileName = file.name
                val mimeType = when (file.name.substringAfterLast('.').lowercase()) {
                    "jpg", "jpeg" -> "image/jpeg"
                    "png" -> "image/png"
                    "gif" -> "image/gif"
                    "webp" -> "image/webp"
                    else -> "image/jpeg"
                }
                
                // Check file size (5MB limit)
                if (fileData.size > 5 * 1024 * 1024) {
                    onError("Gambar terlalu besar. Maksimal 5MB.")
                    return@rememberFilePickerLauncher
                }
                
                onFileSelected(SelectedFile(fileData, fileName, mimeType))
            } catch (e: Exception) {
                onError("Gagal membaca gambar: ${e.message}")
            }
        }
    }
    
    return { launcher.launch() }
}
