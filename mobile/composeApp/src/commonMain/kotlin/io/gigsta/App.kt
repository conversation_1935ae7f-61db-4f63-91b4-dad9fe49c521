package io.gigsta

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.lifecycle.viewmodel.compose.viewModel
import org.jetbrains.compose.ui.tooling.preview.Preview
import io.gigsta.di.AppModule
import io.gigsta.domain.model.AuthState
import io.gigsta.presentation.auth.AuthScreen
import io.gigsta.presentation.auth.AuthViewModel
import io.gigsta.presentation.home.HomeScreen
import io.gigsta.presentation.email.EmailApplicationScreen
import io.gigsta.presentation.email.EmailApplicationViewModel

enum class Screen {
    HOME,
    EMAIL_APPLICATION
}

@Composable
@Preview
fun App() {
    MaterialTheme {
        val authViewModel: AuthViewModel = viewModel { AppModule.provideAuthViewModel() }
        val authState = authViewModel.authState

        // Navigation state
        var currentScreen by remember { mutableStateOf(Screen.HOME) }

        when (authState) {
            is AuthState.Loading -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }
            is AuthState.Unauthenticated -> {
                AuthScreen(viewModel = authViewModel)
            }
            is AuthState.Authenticated -> {
                when (currentScreen) {
                    Screen.HOME -> {
                        HomeScreen(
                            viewModel = viewModel { AppModule.provideHomeViewModel() },
                            onSignOut = { authViewModel.onSignOut() },
                            onNavigateToEmailApplication = {
                                currentScreen = Screen.EMAIL_APPLICATION
                            }
                        )
                    }
                    Screen.EMAIL_APPLICATION -> {
                        EmailApplicationScreen(
                            viewModel = viewModel { AppModule.provideEmailApplicationViewModel() },
                            onNavigateBack = {
                                currentScreen = Screen.HOME
                            }
                        )
                    }
                }
            }
            is AuthState.Error -> {
                AuthScreen(viewModel = authViewModel)
            }
        }
    }
}