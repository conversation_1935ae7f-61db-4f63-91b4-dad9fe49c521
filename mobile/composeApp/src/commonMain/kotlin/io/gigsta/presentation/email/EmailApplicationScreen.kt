package io.gigsta.presentation.email

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.automirrored.filled.ArrowForward
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import io.gigsta.domain.model.EmailGenerationStep
import io.gigsta.presentation.email.components.*
import io.gigsta.presentation.theme.Spacing

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EmailApplicationScreen(
    onNavigateBack: () -> Unit,
    viewModel: EmailApplicationViewModel = viewModel(),
    modifier: Modifier = Modifier
) {
    val uiState = viewModel.uiState
    val scrollState = rememberScrollState()
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "Buat Email Lamaran",
                        fontWeight = FontWeight.SemiBold
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Kembali"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.surface,
                    titleContentColor = MaterialTheme.colorScheme.onSurface
                )
            )
        }
    ) { paddingValues ->
        Column(
            modifier = modifier
                .fillMaxSize()
                .padding(paddingValues)
                .verticalScroll(scrollState)
                .padding(Spacing.medium),
            verticalArrangement = Arrangement.spacedBy(Spacing.large)
        ) {
            // Header description
            Text(
                text = "Dapatkan email lamaran kerja profesional yang dipersonalisasi berdasarkan CV/resume dan informasi pekerjaan Anda—tingkatkan peluang Anda untuk lolos seleksi dengan satu klik!",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            // Step indicator
            StepIndicator(
                currentStep = uiState.currentStep,
                modifier = Modifier.fillMaxWidth()
            )
            
            // Error message
            uiState.error?.let { error ->
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer
                    ),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        text = error,
                        color = MaterialTheme.colorScheme.onErrorContainer,
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(Spacing.medium)
                    )
                }
            }
            
            // Step content
            when (uiState.currentStep) {
                EmailGenerationStep.RESUME_UPLOAD -> {
                    ResumeUploadSection(
                        isLoading = false,
                        uploadSuccess = uiState.isResumeUploaded,
                        existingResume = uiState.resumeInfo,
                        error = if (uiState.error?.contains("resume") == true) uiState.error else null,
                        onFileSelected = { fileData, fileName, mimeType ->
                            viewModel.onResumeUploaded(fileData, fileName, mimeType)
                        },
                        onViewResume = viewModel::onViewResume,
                        onDeleteResume = viewModel::onDeleteResume
                    )
                }
                
                EmailGenerationStep.JOB_INFO -> {
                    JobInfoInputSection(
                        inputMethod = uiState.inputMethod,
                        jobDescription = uiState.jobDescription,
                        onJobDescriptionChange = viewModel::onJobDescriptionChanged,
                        onInputMethodChange = viewModel::onInputMethodChanged,
                        onImageSelected = { imageData, fileName, mimeType ->
                            viewModel.onJobImageSelected(imageData, fileName, mimeType)
                        },
                        hasJobImage = uiState.hasJobImage,
                        error = if (uiState.error?.contains("lowongan") == true || 
                                    uiState.error?.contains("gambar") == true ||
                                    uiState.error?.contains("deskripsi") == true) uiState.error else null
                    )
                }
                
                EmailGenerationStep.RESULT -> {
                    EmailResultSection(
                        emailApplication = uiState.emailApplication,
                        isGenerating = uiState.isGenerating,
                        onRegenerateEmail = viewModel::regenerateEmail
                    )
                }
            }
            
            // Navigation buttons
            NavigationButtons(
                currentStep = uiState.currentStep,
                canProceed = when (uiState.currentStep) {
                    EmailGenerationStep.RESUME_UPLOAD -> uiState.isResumeUploaded
                    EmailGenerationStep.JOB_INFO -> when (uiState.inputMethod) {
                        io.gigsta.domain.model.InputMethod.TEXT -> uiState.jobDescription.isNotBlank()
                        io.gigsta.domain.model.InputMethod.IMAGE -> uiState.hasJobImage
                    }
                    EmailGenerationStep.RESULT -> false
                },
                isGenerating = uiState.isGenerating,
                onNext = viewModel::goToNextStep,
                onBack = viewModel::goToPreviousStep
            )
            
            // Add some bottom padding for better scrolling experience
            Spacer(modifier = Modifier.height(Spacing.large))
        }
    }
}

@Composable
private fun NavigationButtons(
    currentStep: EmailGenerationStep,
    canProceed: Boolean,
    isGenerating: Boolean,
    onNext: () -> Unit,
    onBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(Spacing.medium)
    ) {
        // Back button (not shown on first step)
        if (currentStep != EmailGenerationStep.RESUME_UPLOAD) {
            OutlinedButton(
                onClick = onBack,
                modifier = Modifier.weight(1f),
                enabled = !isGenerating
            ) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                    contentDescription = null,
                    modifier = Modifier.size(16.dp)
                )
                Spacer(modifier = Modifier.width(Spacing.extraSmall))
                Text("Kembali")
            }
        }
        
        // Next/Generate button (not shown on result step)
        if (currentStep != EmailGenerationStep.RESULT) {
            Button(
                onClick = onNext,
                modifier = Modifier.weight(if (currentStep == EmailGenerationStep.RESUME_UPLOAD) 1f else 1f),
                enabled = canProceed && !isGenerating
            ) {
                if (isGenerating) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp,
                        color = MaterialTheme.colorScheme.onPrimary
                    )
                    Spacer(modifier = Modifier.width(Spacing.extraSmall))
                    Text("Membuat...")
                } else {
                    Text(
                        when (currentStep) {
                            EmailGenerationStep.RESUME_UPLOAD -> "Lanjutkan"
                            EmailGenerationStep.JOB_INFO -> "Buat Email"
                            EmailGenerationStep.RESULT -> ""
                        }
                    )
                    if (currentStep == EmailGenerationStep.RESUME_UPLOAD) {
                        Spacer(modifier = Modifier.width(Spacing.extraSmall))
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowForward,
                            contentDescription = null,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                }
            }
        }
    }
}
