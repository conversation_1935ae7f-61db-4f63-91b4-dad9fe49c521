package io.gigsta.presentation.components.history

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import io.gigsta.domain.model.*

@Composable
fun HistoryItemContent(
    historyItem: BaseHistoryItem,
    modifier: Modifier = Modifier
) {
    when (historyItem) {
        is ResumeHistoryItem -> ResumeItemContent(historyItem, modifier)
        is LetterHistoryItem -> LetterItemContent(historyItem, modifier)
        is EmailHistoryItem -> EmailItemContent(historyItem, modifier)
    }
}
