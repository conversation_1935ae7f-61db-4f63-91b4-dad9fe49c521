package io.gigsta.presentation.components.history

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.Article
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import io.gigsta.domain.model.LetterHistoryItem
import io.gigsta.presentation.components.common.*
import io.gigsta.presentation.theme.Spacing

@Composable
fun LetterItemContent(
    historyItem: LetterHistoryItem,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(Spacing.contentSpacing)
    ) {
        // 1. Position applied for (most prominent)
        historyItem.structuredData?.subject?.position?.let { position ->
            TitleText(text = position)
        }

        // 2. Template name (secondary, with icon)
        historyItem.templateName?.let { name ->
            IconTextRow(
                icon = Icons.AutoMirrored.Default.Article,
                text = name
            )
        }

        // 3. Company name or letter content preview
        val previewText = historyItem.structuredData?.recipient?.company?.takeIf { it.isNotBlank() }
            ?: historyItem.structuredData?.body?.paragraphs?.firstOrNull()?.replace("\n", " ")?.trim()

        previewText?.let { text ->
            ContentPreview(text = text)
        }

        // 4. Created date (with icon)
        DateRow(createdAt = historyItem.createdAt)
    }
}
