package io.gigsta.presentation.components.history

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.Feed
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import io.gigsta.domain.model.ResumeHistoryItem
import io.gigsta.presentation.components.common.*
import io.gigsta.presentation.theme.Spacing

@Composable
fun ResumeItemContent(
    historyItem: ResumeHistoryItem,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(Spacing.contentSpacing)
    ) {
        // 1. Target position (most prominent)
        historyItem.structuredData?.targetPosition?.let { position ->
            TitleText(text = position)
        }

        // 2. Template name (secondary)
        historyItem.templateName?.let { name ->
            IconTextRow(
                icon = Icons.AutoMirrored.Default.Feed,
                text = name
            )
        }

        // 3. Professional summary (with label)
        historyItem.structuredData?.professionalSummary?.let { summary ->
            ContentPreview(text = summary)
        }

        // 4. Created date (with icon)
        DateRow(createdAt = historyItem.createdAt)
    }
}
