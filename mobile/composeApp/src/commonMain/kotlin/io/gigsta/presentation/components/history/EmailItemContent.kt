package io.gigsta.presentation.components.history

import androidx.compose.foundation.layout.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import io.gigsta.domain.model.EmailHistoryItem
import io.gigsta.presentation.components.common.*
import io.gigsta.presentation.theme.Spacing

@Composable
fun EmailItemContent(
    historyItem: EmailHistoryItem,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(Spacing.contentSpacing)
    ) {
        // 1. Email subject (most prominent)
        TitleText(text = historyItem.subject)

        // 2. Email body preview
        ContentPreview(
            text = historyItem.body.replace("\n", " ").trim()
        )

        // 3. Created date (with icon)
        DateRow(createdAt = historyItem.createdAt)
    }
}
