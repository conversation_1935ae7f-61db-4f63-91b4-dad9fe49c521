package io.gigsta.presentation.home

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.Article
import androidx.compose.material.icons.automirrored.filled.Feed
import androidx.compose.material.icons.filled.Article
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.Send
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import io.gigsta.domain.model.*
import io.gigsta.domain.model.MenuItem
import io.gigsta.domain.usecase.GetHistoryItemsUseCase
import kotlinx.coroutines.launch

class HomeViewModel(
    private val getHistoryItemsUseCase: GetHistoryItemsUseCase
) : ViewModel() {

    var uiState by mutableStateOf(HomeUiState())
        private set

    private var onNavigateToEmailApplication: (() -> Unit)? = null

    val menuItems = listOf(
        MenuItem("resume_builder", "CV ATS", Icons.AutoMirrored.Default.Feed, HistoryItemType.CV_BUILDER),
        MenuItem("application_letter", "Surat Lamaran", Icons.AutoMirrored.Default.Article, HistoryItemType.APPLICATION_LETTER),
        MenuItem("email_application", "Email Lamaran", Icons.Default.Email, HistoryItemType.EMAIL_APPLICATION),
//        MenuItem("job_match", "Job Match", Icons.Default.Search, HistoryItemType.JOB_MATCH)
    )

    init {
        loadHistoryItems()
    }

    fun setNavigationCallbacks(
        onNavigateToEmailApplication: () -> Unit
    ) {
        this.onNavigateToEmailApplication = onNavigateToEmailApplication
    }

    fun onTabSelected(index: Int) {
        uiState = uiState.copy(selectedTabIndex = index)
        loadHistoryItems()
    }

    fun onCreateNewItem() {
        val currentType = menuItems[uiState.selectedTabIndex].type
        when (currentType) {
            HistoryItemType.EMAIL_APPLICATION -> {
                onNavigateToEmailApplication?.invoke()
            }
            HistoryItemType.CV_BUILDER -> {
                // TODO: Navigate to CV builder screen
            }
            HistoryItemType.APPLICATION_LETTER -> {
                // TODO: Navigate to application letter screen
            }
            else -> {
                // Handle other types
            }
        }
    }

    fun onHistoryItemClick(item: BaseHistoryItem) {
        // TODO: Navigate to detail/edit screen based on item type
    }
    
    private fun loadHistoryItems() {
        val currentType = menuItems[uiState.selectedTabIndex].type
        
        viewModelScope.launch {
            uiState = uiState.copy(isLoading = true)
            try {
                val items = getHistoryItemsUseCase(currentType)
                uiState = uiState.copy(
                    historyItems = items,
                    isLoading = false,
                    error = null
                )
            } catch (e: Exception) {
                uiState = uiState.copy(
                    isLoading = false,
                    error = e.message ?: "Unknown error occurred"
                )
            }
        }
    }
}

data class HomeUiState(
    val selectedTabIndex: Int = 0,
    val historyItems: List<BaseHistoryItem> = emptyList(),
    val isLoading: Boolean = false,
    val error: String? = null
)
