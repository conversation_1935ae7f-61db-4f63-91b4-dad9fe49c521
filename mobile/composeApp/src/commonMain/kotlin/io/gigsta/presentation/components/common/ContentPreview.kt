package io.gigsta.presentation.components.common

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.TextUnit
import io.gigsta.presentation.theme.AppTypography
import io.gigsta.presentation.theme.Spacing

@Composable
fun ContentPreview(
    text: String,
    modifier: Modifier = Modifier,
    fontSize: TextUnit = AppTypography.bodyMedium,
    maxLines: Int = 2,
    lineHeight: TextUnit = AppTypography.contentLineHeight
) {
    if (text.isNotBlank()) {
        Column(
            modifier = modifier,
            verticalArrangement = Arrangement.spacedBy(Spacing.extraSmall / 2)
        ) {
            Text(
                text = text,
                fontSize = fontSize,
                fontWeight = FontWeight.Normal,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                maxLines = maxLines,
                overflow = TextOverflow.Ellipsis,
                lineHeight = lineHeight
            )
        }
    }
}
