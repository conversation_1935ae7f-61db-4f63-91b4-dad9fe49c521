package io.gigsta.presentation.home.components

import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.unit.dp
import io.gigsta.presentation.theme.AppTypography
import io.gigsta.domain.model.MenuItem

@Composable
fun HomeBottomNavigationBar(
    selectedTabIndex: Int,
    onTabSelected: (Int) -> Unit,
    menuItems: List<MenuItem>
) {
    NavigationBar(
        containerColor = MaterialTheme.colorScheme.surface,
        tonalElevation = 8.dp
    ) {
        menuItems.forEachIndexed { index, menuItem ->
            NavigationBarItem(
                icon = {
                    Icon(
                        imageVector = menuItem.icon,
                        contentDescription = menuItem.title
                    )
                },
                label = {
                    Text(
                        text = menuItem.title,
                        fontSize = AppTypography.bodySmall
                    )
                },
                selected = selectedTabIndex == index,
                onClick = { onTabSelected(index) },
                colors = NavigationBarItemDefaults.colors(
                    selectedIconColor = MaterialTheme.colorScheme.primary,
                    selectedTextColor = MaterialTheme.colorScheme.primary,
                    unselectedIconColor = MaterialTheme.colorScheme.onSurfaceVariant,
                    unselectedTextColor = MaterialTheme.colorScheme.onSurfaceVariant,
                    indicatorColor = MaterialTheme.colorScheme.primaryContainer
                )
            )
        }
    }
}
