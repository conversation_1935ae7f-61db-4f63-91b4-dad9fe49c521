package io.gigsta.presentation.email.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.CloudUpload
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import io.gigsta.presentation.components.common.ErrorMessage
import io.gigsta.presentation.theme.Spacing

/**
 * Component for resume upload functionality
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ResumeUploadSection(
    isLoading: <PERSON><PERSON><PERSON>,
    uploadSuccess: Boolean,
    existingResume: ResumeInfo?,
    error: String?,
    onFileSelected: (ByteArray, String, String) -> Unit,
    onViewResume: () -> Unit,
    onDeleteResume: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(Spacing.medium)
    ) {
        Text(
            text = "1. Upload CV/Resume Anda",
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.SemiBold
        )
        
        if (isLoading) {
            LoadingCard()
        } else if (uploadSuccess && existingResume != null) {
            ExistingResumeCard(
                resumeInfo = existingResume,
                onViewResume = onViewResume,
                onDeleteResume = onDeleteResume
            )
        } else {
            UploadCard(onFileSelected = onFileSelected)
        }
        
        error?.let { errorMessage ->
            ErrorMessage(message = errorMessage)
        }
    }
}

@Composable
private fun LoadingCard() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Spacing.large),
            contentAlignment = Alignment.Center
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(Spacing.medium)
            ) {
                CircularProgressIndicator(
                    modifier = Modifier.size(24.dp),
                    strokeWidth = 2.dp
                )
                Text(
                    text = "Memuat...",
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ExistingResumeCard(
    resumeInfo: ResumeInfo,
    onViewResume: () -> Unit,
    onDeleteResume: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFF0F9FF) // Light blue background
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(Spacing.large),
            verticalArrangement = Arrangement.spacedBy(Spacing.medium)
        ) {
            // Header with success icon
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(Spacing.small)
            ) {
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = null,
                    tint = Color(0xFF10B981), // Green color
                    modifier = Modifier.size(24.dp)
                )
                Text(
                    text = "Resume aktif tersedia!",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF065F46) // Dark green
                )
            }
            
            // File info
            Column(
                verticalArrangement = Arrangement.spacedBy(Spacing.extraSmall)
            ) {
                Text(
                    text = "File: ${resumeInfo.displayName}",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Text(
                    text = "Diunggah pada: ${resumeInfo.uploadedAt}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            // Action buttons
            Row(
                horizontalArrangement = Arrangement.spacedBy(Spacing.medium)
            ) {
                OutlinedButton(
                    onClick = onViewResume,
                    modifier = Modifier.weight(1f)
                ) {
                    Icon(
                        imageVector = Icons.Default.Visibility,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(Spacing.extraSmall))
                    Text("Lihat Resume")
                }
                
                OutlinedButton(
                    onClick = onDeleteResume,
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(Spacing.extraSmall))
                    Text("Ganti Resume")
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun UploadCard(
    onFileSelected: (ByteArray, String, String) -> Unit
) {
    var showFilePicker by remember { mutableStateOf(false) }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Spacing.large),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(Spacing.medium)
        ) {
            Icon(
                imageVector = Icons.Default.CloudUpload,
                contentDescription = null,
                modifier = Modifier.size(48.dp),
                tint = MaterialTheme.colorScheme.primary
            )
            
            Text(
                text = "Upload CV/Resume Anda",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium,
                textAlign = TextAlign.Center
            )
            
            Text(
                text = "Format yang didukung: PDF, DOCX, PNG, JPG\nMaksimal 5MB",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                textAlign = TextAlign.Center
            )
            
            Button(
                onClick = { showFilePicker = true },
                modifier = Modifier.fillMaxWidth()
            ) {
                Text("Pilih File")
            }
        }
    }
    
    // File picker implementation
    if (showFilePicker) {
        LaunchedEffect(showFilePicker) {
            // For now, show a message that file picker is not implemented
            // In a real implementation, you would use the FilePicker class
            showFilePicker = false
            // TODO: Implement file picker integration
            // val filePicker = FilePicker(context)
            // filePicker.pickFile(
            //     mimeTypes = FilePickerUtils.RESUME_MIME_TYPES,
            //     onFileSelected = onFileSelected,
            //     onError = { error -> /* handle error */ }
            // )
        }
    }
}

/**
 * Data class for resume information
 */
data class ResumeInfo(
    val fileName: String,
    val displayName: String,
    val uploadedAt: String,
    val url: String? = null
)
