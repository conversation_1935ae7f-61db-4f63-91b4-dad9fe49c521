package io.gigsta.presentation.home.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import io.gigsta.domain.model.BaseHistoryItem
import io.gigsta.domain.model.MenuItem
import io.gigsta.presentation.components.cards.ActionCard
import io.gigsta.presentation.components.cards.HistoryCard
import io.gigsta.presentation.components.common.EmptyState
import io.gigsta.presentation.components.common.ErrorMessage
import io.gigsta.presentation.components.common.LoadingIndicator
import io.gigsta.presentation.components.history.HistoryItemContent
import io.gigsta.presentation.theme.Spacing

@Composable
fun HomeContent(
    modifier: Modifier = Modifier,
    menuItem: MenuItem,
    historyItems: List<BaseHistoryItem>,
    isLoading: Boolean,
    error: String?,
    onCreateNewItem: () -> Unit,
    onHistoryItemClick: (BaseHistoryItem) -> Unit
) {
    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .padding(horizontal = Spacing.medium),
        verticalArrangement = Arrangement.spacedBy(Spacing.medium)
    ) {
        item {
            ActionCard(
                text = "Tambah ${menuItem.title}",
                onClick = onCreateNewItem
            )
        }

        when {
            isLoading -> {
                item {
                    LoadingIndicator()
                }
            }
            error != null -> {
                item {
                    ErrorMessage(message = error)
                }
            }
            historyItems.isEmpty() -> {
                item {
                    EmptyState(
                        title = "Belum ada ${menuItem.title}",
                        subtitle = "Buat ${menuItem.title} untuk memulai"
                    )
                }
            }
            else -> {
                items(historyItems) { historyItem ->
                    HistoryCard(
                        onClick = { onHistoryItemClick(historyItem) }
                    ) {
                        HistoryItemContent(historyItem = historyItem)
                    }
                }
            }
        }

        item {
            Spacer(modifier = Modifier.height(Spacing.medium))
        }
    }
}
