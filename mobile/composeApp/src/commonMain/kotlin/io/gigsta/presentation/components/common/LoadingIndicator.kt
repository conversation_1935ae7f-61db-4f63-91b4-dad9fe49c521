package io.gigsta.presentation.components.common

import androidx.compose.foundation.layout.*
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import io.gigsta.presentation.theme.Spacing

@Composable
fun LoadingIndicator(
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .padding(Spacing.extraLarge),
        contentAlignment = Alignment.Center
    ) {
        CircularProgressIndicator()
    }
}
