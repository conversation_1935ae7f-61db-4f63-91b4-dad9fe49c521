package io.gigsta.presentation.components.common

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Schedule
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import io.gigsta.presentation.theme.AppTypography
import io.gigsta.presentation.theme.Spacing
import io.gigsta.domain.utils.DateUtils

@Composable
fun DateRow(
    createdAt: String,
    modifier: Modifier = Modifier,
    prefix: String = "Dibuat pada "
) {
    IconTextRow(
        modifier = modifier,
        icon = Icons.Default.Schedule,
        text = "$prefix${DateUtils.formatDateForIndonesianLocale(createdAt)}",
        iconSize = Spacing.small + Spacing.extraSmall,
        iconColor = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f),
        textColor = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.8f),
        fontSize = AppTypography.bodySmall,
        fontWeight = AppTypography.normal
    )
}
