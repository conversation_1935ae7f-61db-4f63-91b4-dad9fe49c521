package io.gigsta.data.network

object NetworkConfig {
    // TODO: Move these to build configuration or environment variables
    // These should match the values from your web app's .env file
//    const val SUPABASE_URL = "https://your-project.supabase.co"
//    const val SUPABASE_ANON_KEY = "your-anon-key"

    // For development, you might want to use local Supabase instance
     const val SUPABASE_URL = "http://10.0.2.2:54321"
     const val SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0"

    // API Base URL - for development, use local Next.js server
    // In production, this should be your deployed web app URL
    const val API_BASE_URL = "http://10.0.2.2:3000"
}
