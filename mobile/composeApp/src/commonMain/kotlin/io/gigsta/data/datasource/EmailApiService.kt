package io.gigsta.data.datasource

import io.gigsta.data.model.EmailApplicationRequest
import io.gigsta.data.model.EmailApplicationResponse
import io.gigsta.data.network.HttpClient
import io.gigsta.data.network.NetworkConfig
import io.ktor.client.call.*
import io.ktor.client.request.*
import io.ktor.client.request.forms.*
import io.ktor.client.statement.*
import io.ktor.http.*

class EmailApiService {

    private val httpClient = HttpClient.client

    suspend fun generateEmailApplication(request: EmailApplicationRequest): Result<EmailApplicationResponse> {
        return try {
            val response: HttpResponse = httpClient.post("${NetworkConfig.API_BASE_URL}/api/generate-email-application") {
                body = MultiPartFormDataContent(
                    formData {
                        // Add job description if provided
                        request.jobDescription?.let { jobDesc ->
                            append("jobDescription", jobDesc)
                        }

                        // Add job image if provided
                        request.jobImage?.let { imageBytes ->
                            append(
                                "jobImage",
                                imageBytes,
                                Headers.build {
                                    append(HttpHeaders.ContentType, request.jobImageMimeType ?: "image/jpeg")
                                    append(HttpHeaders.ContentDisposition, "filename=job_image")
                                }
                            )
                        }

                        // Add resume file if provided
                        request.unauthenticatedResumeFile?.let { resumeBytes ->
                            append(
                                "unauthenticatedResumeFile",
                                resumeBytes,
                                Headers.build {
                                    append(HttpHeaders.ContentType, getMimeTypeFromFileName(request.unauthenticatedResumeFileName))
                                    append(HttpHeaders.ContentDisposition, "filename=${request.unauthenticatedResumeFileName}")
                                }
                            )
                        }

                        // Add resume filename if provided
                        request.unauthenticatedResumeFileName?.let { fileName ->
                            append("unauthenticatedResumeFileName", fileName)
                        }
                    }
                )
            }

            if (response.status.isSuccess()) {
                val emailResponse = response.body<EmailApplicationResponse>()
                Result.success(emailResponse)
            } else {
                val errorBody = response.bodyAsText()
                Result.failure(Exception("API Error: ${response.status.value} - $errorBody"))
            }

        } catch (e: Exception) {
            println("Error generating email application: ${e.message}")
            Result.failure(Exception("Failed to generate email application: ${e.message}"))
        }
    }

    private fun getMimeTypeFromFileName(fileName: String?): String {
        return when (fileName?.lowercase()?.substringAfterLast('.')) {
            "pdf" -> "application/pdf"
            "docx" -> "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            "doc" -> "application/msword"
            "txt" -> "text/plain"
            "png" -> "image/png"
            "jpg", "jpeg" -> "image/jpeg"
            else -> "application/octet-stream"
        }
    }
}
