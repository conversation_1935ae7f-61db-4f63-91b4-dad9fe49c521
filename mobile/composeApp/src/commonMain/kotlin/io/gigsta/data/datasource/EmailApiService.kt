package io.gigsta.data.datasource

import io.gigsta.data.model.EmailApplicationRequest
import io.gigsta.data.model.EmailApplicationResponse
import io.gigsta.data.network.HttpClient
import io.gigsta.data.network.NetworkConfig
import io.ktor.client.call.*
import io.ktor.client.request.*
import io.ktor.client.request.forms.*
import io.ktor.client.statement.*
import io.ktor.http.*

class EmailApiService {

    private val httpClient = HttpClient.client

    suspend fun generateEmailApplication(request: EmailApplicationRequest): Result<EmailApplicationResponse> {
        return try {
            // Build the form data as a List<PartData>
            val formDataParts = buildList<PartData> {
                // Add job description if provided
                request.jobDescription?.let { jobDesc ->
                    add(PartData.FormItem(
                        value = jobDesc,
                        dispose = {},
                        partHeaders = Headers.build {
                            append(HttpHeaders.ContentDisposition, "form-data; name=\"jobDescription\"")
                        }
                    ))
                }

                // Add job image if provided
                request.jobImage?.let { imageBytes ->
                    add(PartData.BinaryChannelItem(
                        provider = { ByteReadChannel(imageBytes) },
                        dispose = {},
                        partHeaders = Headers.build {
                            append(HttpHeaders.ContentDisposition, "form-data; name=\"jobImage\"; filename=\"job_image\"")
                            append(HttpHeaders.ContentType, request.jobImageMimeType ?: "image/jpeg")
                        }
                    ))
                }

                // Add resume file if provided
                request.unauthenticatedResumeFile?.let { resumeBytes ->
                    add(PartData.BinaryChannelItem(
                        provider = { ByteReadChannel(resumeBytes) },
                        dispose = {},
                        partHeaders = Headers.build {
                            append(HttpHeaders.ContentDisposition, "form-data; name=\"unauthenticatedResumeFile\"; filename=\"${request.unauthenticatedResumeFileName}\"")
                            append(HttpHeaders.ContentType, getMimeTypeFromFileName(request.unauthenticatedResumeFileName))
                        }
                    ))
                }

                // Add resume filename if provided
                request.unauthenticatedResumeFileName?.let { fileName ->
                    add(PartData.FormItem(
                        value = fileName,
                        dispose = {},
                        partHeaders = Headers.build {
                            append(HttpHeaders.ContentDisposition, "form-data; name=\"unauthenticatedResumeFileName\"")
                        }
                    ))
                }
            }

            val response: HttpResponse = httpClient.submitFormWithBinaryData(
                url = "${NetworkConfig.API_BASE_URL}/api/generate-email-application",
                formData = formDataParts
            )

            if (response.status.isSuccess()) {
                val emailResponse = response.body<EmailApplicationResponse>()
                Result.success(emailResponse)
            } else {
                val errorBody = response.bodyAsText()
                Result.failure(Exception("API Error: ${response.status.value} - $errorBody"))
            }

        } catch (e: Exception) {
            println("Error generating email application: ${e.message}")
            Result.failure(Exception("Failed to generate email application: ${e.message}"))
        }
    }

    private fun getMimeTypeFromFileName(fileName: String?): String {
        return when (fileName?.lowercase()?.substringAfterLast('.')) {
            "pdf" -> "application/pdf"
            "docx" -> "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            "doc" -> "application/msword"
            "txt" -> "text/plain"
            "png" -> "image/png"
            "jpg", "jpeg" -> "image/jpeg"
            else -> "application/octet-stream"
        }
    }
}
