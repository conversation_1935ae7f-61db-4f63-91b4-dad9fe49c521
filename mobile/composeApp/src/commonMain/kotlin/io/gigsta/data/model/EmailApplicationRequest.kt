package io.gigsta.data.model

import kotlinx.serialization.Serializable

@Serializable
data class EmailApplicationRequest(
    val jobDescription: String? = null,
    val jobImage: ByteArray? = null,
    val jobImageMimeType: String? = null,
    val unauthenticatedResumeFile: ByteArray? = null,
    val unauthenticatedResumeFileName: String? = null
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other == null || this::class != other::class) return false

        other as EmailApplicationRequest

        if (jobDescription != other.jobDescription) return false
        if (jobImage != null) {
            if (other.jobImage == null) return false
            if (!jobImage.contentEquals(other.jobImage)) return false
        } else if (other.jobImage != null) return false
        if (jobImageMimeType != other.jobImageMimeType) return false
        if (unauthenticatedResumeFile != null) {
            if (other.unauthenticatedResumeFile == null) return false
            if (!unauthenticatedResumeFile.contentEquals(other.unauthenticatedResumeFile)) return false
        } else if (other.unauthenticatedResumeFile != null) return false
        if (unauthenticatedResumeFileName != other.unauthenticatedResumeFileName) return false

        return true
    }

    override fun hashCode(): Int {
        var result = jobDescription?.hashCode() ?: 0
        result = 31 * result + (jobImage?.contentHashCode() ?: 0)
        result = 31 * result + (jobImageMimeType?.hashCode() ?: 0)
        result = 31 * result + (unauthenticatedResumeFile?.contentHashCode() ?: 0)
        result = 31 * result + (unauthenticatedResumeFileName?.hashCode() ?: 0)
        return result
    }
}

@Serializable
data class EmailApplicationResult(
    val subject: String,
    val body: String
)

@Serializable
data class EmailApplicationResponse(
    val success: Boolean,
    val emailApplication: EmailApplicationResult? = null,
    val emailId: String? = null,
    val error: String? = null
)
