package io.gigsta.di

import io.gigsta.data.repository.AuthRepositoryImpl
import io.gigsta.data.repository.HistoryRepositoryImpl
import io.gigsta.data.repository.EmailApplicationRepositoryImpl
import io.gigsta.data.datasource.EmailApiService
import io.gigsta.data.network.HttpClient
import io.gigsta.domain.repository.AuthRepository
import io.gigsta.domain.repository.HistoryRepository
import io.gigsta.domain.repository.EmailApplicationRepository
import io.gigsta.domain.usecase.GetHistoryItemsUseCase
import io.gigsta.domain.usecase.SignInUseCase
import io.gigsta.domain.usecase.SignUpUseCase
import io.gigsta.domain.usecase.SignOutUseCase
import io.gigsta.domain.usecase.GenerateEmailApplicationUseCase
import io.gigsta.presentation.auth.AuthViewModel
import io.gigsta.presentation.home.HomeViewModel
import io.gigsta.presentation.email.EmailApplicationViewModel

object AppModule {

    // Repositories
    private val authRepository: AuthRepository by lazy {
        AuthRepositoryImpl()
    }

    private val historyRepository: HistoryRepository by lazy {
        HistoryRepositoryImpl(authRepository)
    }

    private val emailApiService: EmailApiService by lazy {
        EmailApiService(HttpClient.create())
    }

    private val emailApplicationRepository: EmailApplicationRepository by lazy {
        EmailApplicationRepositoryImpl(emailApiService)
    }

    // Use Cases
    private val signInUseCase: SignInUseCase by lazy {
        SignInUseCase(authRepository)
    }

    private val signUpUseCase: SignUpUseCase by lazy {
        SignUpUseCase(authRepository)
    }

    private val signOutUseCase: SignOutUseCase by lazy {
        SignOutUseCase(authRepository)
    }

    private val getHistoryItemsUseCase: GetHistoryItemsUseCase by lazy {
        GetHistoryItemsUseCase(historyRepository)
    }

    private val generateEmailApplicationUseCase: GenerateEmailApplicationUseCase by lazy {
        GenerateEmailApplicationUseCase(emailApplicationRepository)
    }

    // ViewModels
    fun provideAuthViewModel(): AuthViewModel {
        return AuthViewModel(
            authRepository = authRepository,
            signInUseCase = signInUseCase,
            signUpUseCase = signUpUseCase,
            signOutUseCase = signOutUseCase
        )
    }

    fun provideHomeViewModel(): HomeViewModel {
        return HomeViewModel(getHistoryItemsUseCase)
    }

    fun provideEmailApplicationViewModel(): EmailApplicationViewModel {
        return EmailApplicationViewModel(generateEmailApplicationUseCase)
    }
}
