package io.gigsta.domain.usecase

import io.gigsta.domain.model.EmailApplication
import io.gigsta.domain.model.JobInfo
import io.gigsta.domain.model.ResumeInfo
import io.gigsta.domain.repository.EmailApplicationRepository

class GenerateEmailApplicationUseCase(
    private val repository: EmailApplicationRepository
) {
    suspend operator fun invoke(
        resumeInfo: ResumeInfo,
        jobInfo: JobInfo
    ): Result<EmailApplication> {
        return repository.generateEmailApplication(resumeInfo, jobInfo)
    }
}
