package io.gigsta.domain.model

import io.gigsta.domain.utils.StructuredLetterData
import kotlinx.serialization.Serializable

sealed interface BaseHistoryItem {
    val id: String
    val type: HistoryItemType
    val createdAt: String
}

data class ResumeHistoryItem(
    override val id: String,
    override val type: HistoryItemType = HistoryItemType.CV_BUILDER,
    override val createdAt: String,
    val structuredData: StructuredResumeData?,
    val htmlContent: String?,
    val templateId: String,
    val templateName: String?,
    val tokensDeducted: Boolean
) : BaseHistoryItem

data class LetterHistoryItem(
    override val id: String,
    override val type: HistoryItemType = HistoryItemType.APPLICATION_LETTER,
    override val createdAt: String,
    val templateId: String,
    val templateName: String?,
    val plainText: String?,
    val designHtml: String?,
    val structuredData: StructuredLetterData?,
) : BaseHistoryItem

data class EmailHistoryItem(
    override val id: String,
    override val type: HistoryItemType = HistoryItemType.EMAIL_APPLICATION,
    override val createdAt: String,
    val subject: String,
    val body: String
) : BaseHistoryItem

// Structured Resume Data Classes (matching TypeScript StructuredResumeData)
@Serializable
data class StructuredResumeData(
    val personalInfo: PersonalInfo? = null,
    val professionalSummary: String? = null,
    val targetPosition: String? = null,
    val experiences: List<Experience>? = null,
    val education: List<Education>? = null,
    val skills: Skills? = null,
    val certifications: List<Certification>? = null,
    val projects: List<Project>? = null,
    val languages: List<Language>? = null,
    val awards: List<Award>? = null,
    val metadata: Metadata? = null
)

@Serializable
data class PersonalInfo(
    val fullName: String? = null,
    val email: String? = null,
    val phone: String? = null,
    val linkedin: String? = null,
    val location: String? = null,
    val website: String? = null,
    val github: String? = null
)

@Serializable
data class Experience(
    val id: String? = null,
    val jobTitle: String? = null,
    val company: String? = null,
    val location: String? = null,
    val startDate: String? = null,
    val endDate: String? = null, // "Present" for current job
    val responsibilities: List<String>? = null // Array of achievement bullets
)

@Serializable
data class Education(
    val id: String? = null,
    val degree: String? = null,
    val institution: String? = null,
    val location: String? = null,
    val graduationDate: String? = null,
    val gpa: String? = null,
    val relevantCoursework: List<String>? = null,
    val honors: List<String>? = null
)

@Serializable
data class Skills(
    val categories: List<SkillCategory>? = null,
    val allSkills: List<String>? = null // Alternative flat structure for simpler templates
)

@Serializable
data class SkillCategory(
    val category: String? = null,
    val skills: List<String>? = null // Array of skills in this category
)

@Serializable
data class Certification(
    val id: String? = null,
    val name: String? = null,
    val issuer: String? = null,
    val date: String? = null,
    val credentialId: String? = null
)

@Serializable
data class Project(
    val id: String? = null,
    val title: String? = null,
    val description: String? = null,
    val technologies: List<String>? = null,
    val link: String? = null,
    val achievements: List<String>? = null
)

@Serializable
data class Language(
    val language: String? = null,
    val proficiency: String? = null
)

@Serializable
data class Award(
    val id: String? = null,
    val title: String? = null,
    val issuer: String? = null,
    val date: String? = null,
    val description: String? = null
)

@Serializable
data class Metadata(
    val generatedAt: String? = null,
    val lastModified: String? = null,
    val templateId: String? = null,
    val aiSuggestions: List<String>? = null
)

enum class HistoryItemType {
    CV_BUILDER,
    APPLICATION_LETTER,
    EMAIL_APPLICATION,
}