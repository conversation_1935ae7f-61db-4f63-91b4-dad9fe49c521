package io.gigsta.domain.utils

import kotlinx.datetime.Instant
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime

/**
 * Utility functions for date formatting and manipulation
 */
object DateUtils {
    
    /**
     * Format date string in Indonesian locale format
     * Converts ISO date string to "dd MMM yyyy" format with Indonesian month names
     *
     * @param dateString ISO date string (e.g., "2024-01-15T10:30:00Z")
     * @return Formatted date string (e.g., "15 Jan 2024")
     */
    fun formatDateForIndonesianLocale(dateString: String): String {
        return try {
            val instant = Instant.parse(dateString)
            val localDateTime = instant.toLocalDateTime(TimeZone.currentSystemDefault())

            // Indonesian month abbreviations
            val months = listOf(
                "Jan", "Feb", "Mar", "Apr", "Mei", "Jun",
                "Jul", "Agu", "Sep", "Okt", "Nov", "Des"
            )

            val day = localDateTime.dayOfMonth.toString().padStart(2, '0')
            val month = months[localDateTime.monthNumber - 1]
            val year = localDateTime.year

            "$day $month $year"
        } catch (e: Exception) {
            // Fallback to original string if parsing fails
            dateString
        }
    }

    /**
     * Format date string in English locale
     * 
     * @param dateString ISO date string
     * @return Formatted date string with English month names
     */
    fun formatDateForEnglishLocale(dateString: String): String {
        return try {
            val instant = Instant.parse(dateString)
            val localDateTime = instant.toLocalDateTime(TimeZone.currentSystemDefault())
            
            // English month abbreviations
            val months = listOf(
                "Jan", "Feb", "Mar", "Apr", "May", "Jun",
                "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"
            )
            
            val day = localDateTime.dayOfMonth.toString().padStart(2, '0')
            val month = months[localDateTime.monthNumber - 1]
            val year = localDateTime.year
            
            "$day $month $year"
        } catch (e: Exception) {
            // Fallback to original string if parsing fails
            dateString
        }
    }
    
    /**
     * Format date string for relative time display in Indonesian
     *
     * @param dateString ISO date string
     * @return Relative time string (e.g., "2 hari yang lalu", "1 minggu yang lalu")
     */
    fun formatRelativeTimeInIndonesian(dateString: String): String {
        return try {
            val instant = Instant.parse(dateString)
            val now = kotlinx.datetime.Clock.System.now()
            val duration = now - instant
            
            val days = duration.inWholeDays
            val hours = duration.inWholeHours
            val minutes = duration.inWholeMinutes
            
            when {
                days > 0 -> {
                    when {
                        days == 1L -> "1 hari yang lalu"
                        days < 7 -> "$days hari yang lalu"
                        days < 30 -> "${days / 7} minggu yang lalu"
                        days < 365 -> "${days / 30} bulan yang lalu"
                        else -> "${days / 365} tahun yang lalu"
                    }
                }
                hours > 0 -> {
                    if (hours == 1L) "1 jam yang lalu" else "$hours jam yang lalu"
                }
                minutes > 0 -> {
                    if (minutes == 1L) "1 menit yang lalu" else "$minutes menit yang lalu"
                }
                else -> "Baru saja"
            }
        } catch (e: Exception) {
            // Fallback to formatted date
            formatDateForIndonesianLocale(dateString)
        }
    }
}
